{"name": "app", "version": "1.0.0", "description": "", "main": "dist/game.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build": "vite build", "start": "vite", "dev": "vite", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "quality": "npm run type-check && npm run lint && npm run format:check", "quality:fix": "npm run type-check && npm run lint:fix && npm run format", "pre-commit": "lint-staged", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/iceman1212/webgame.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/iceman1212/webgame/issues"}, "homepage": "https://github.com/iceman1212/webgame#readme", "devDependencies": {"@eslint/js": "^9.28.0", "@types/jest": "^29.5.14", "@types/node": "^24.0.0", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "@vitejs/plugin-legacy": "^6.1.1", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "http-server": "^14.1.1", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "ts-jest": "^29.3.4", "typescript": "^5.8.3", "vite": "^6.3.5"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}