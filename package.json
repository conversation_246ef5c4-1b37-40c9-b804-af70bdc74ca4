{"name": "app", "version": "1.0.0", "description": "", "main": "dist/game.js", "scripts": {"test": "jest", "build": "vite build", "start": "vite", "dev": "vite"}, "repository": {"type": "git", "url": "git+https://github.com/iceman1212/webgame.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/iceman1212/webgame/issues"}, "homepage": "https://github.com/iceman1212/webgame#readme", "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^24.0.0", "@vitejs/plugin-legacy": "^6.1.1", "http-server": "^14.1.1", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0", "ts-jest": "^29.3.4", "typescript": "^5.8.3", "vite": "^6.3.5"}}