{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "files.associations": {"*.ts": "typescript", "*.tsx": "typescriptreact"}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/coverage": true, "**/.git": true, "**/.DS_Store": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/coverage": true}, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}}