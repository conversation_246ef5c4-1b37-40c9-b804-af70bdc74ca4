body { margin:0; padding:0; background:#87cefa; overflow:hidden; font-family:"Arial",sans-serif; }
    #gameCanvas { display:block; margin:20px auto; background:linear-gradient(to bottom,#a0e3f7,#ffffff); border-radius:16px; box-shadow:0 0 20px rgba(0,0,0,0.2); }
    #info { position:absolute; top:10px; width:100%; text-align:center; color:#fff; font-size:22px; text-shadow:2px 2px 4px rgba(0,0,0,0.5); pointer-events:none; }
    #info span { background:rgba(0,0,0,0.3); padding:6px 12px; border-radius:8px; margin:0 8px; display:inline-block; }
    #startBtn { position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); padding:16px 32px; background:#fdd835; border:3px solid #fbc02d; border-radius:12px; font-size:26px; font-weight:bold; color:#333; cursor:pointer; box-shadow:0 0 10px rgba(0,0,0,0.3); transition:transform 0.1s ease; }
    #startBtn:hover { transform:translate(-50%,-50%) scale(1.08); }
    #overlay { position:absolute; top:0; left:0; width:100%; height:100%; display:flex; justify-content:center; align-items:center; background:rgba(0,0,0,0.6); visibility:hidden; }
    #messageBox { background:#fff; padding:24px; border-radius:12px; text-align:center; box-shadow:0 0 20px rgba(0,0,0,0.3); max-width:80%; }
    #messageBox h1 { margin:0 0 16px; color:#00796b; font-size:32px; }
    #messageBox p { margin:8px 0; font-size:20px; color:#555; }
    #messageBox button { margin-top:16px; padding:10px 20px; background:#00796b; color:#fff; border:none; border-radius:8px; font-size:20px; cursor:pointer; transition:background 0.2s ease; }
    #messageBox button:hover { background:#004d40; }
